import { Candles } from './broker/channels/candles'
import { SelectAsset } from './broker/channels/selectAsset'
import type { PocketOption } from './broker/PocketOption'
import { Action } from './types/ordersEnum'
import { logger } from './utils/logger'

/**
 * Enhanced PocketOption API wrapper with proper error handling and type safety
 */
export class PocketOptionApi {
	private broker: PocketOption
	private isConnected: boolean = false

	constructor(broker: PocketOption) {
		this.broker = broker
	}

	/**
	 * Connect to the PocketOption broker
	 * @returns Promise that resolves when connection is established
	 */
	async connect(): Promise<void> {
		try {
			await this.broker.connect()
			this.isConnected = true
			logger.success('PocketOptionApi', 'Successfully connected to broker')
		} catch (error) {
			this.isConnected = false
			logger.error('PocketOptionApi', 'Failed to connect to broker', error)
			throw error
		}
	}

	/**
	 * Get the current account balance
	 * @returns Promise that resolves to the current balance
	 */
	async getBalance(): Promise<number> {
		this.ensureConnected()
		try {
			const balance = await this.broker.getBalance()
			logger.debug('PocketOptionApi', `Current balance: ${balance}`)
			return balance
		} catch (error) {
			logger.error('PocketOptionA<PERSON>', 'Failed to get balance', error)
			throw new Error(`Failed to get balance: ${error instanceof Error ? error.message : 'Unknown error'}`)
		}
	}

	async getCandles(assetSymbol: string): Promise<unknown[]> {
		this.ensureConnected()
		try {
			const candles = await this.broker.getCandles(assetSymbol)

			return candles
		} catch (error) {
			logger.error('PocketOptionApi', 'Failed to get candles', error)
			throw new Error(`Failed to get candles: ${error instanceof Error ? error.message : 'Unknown error'}`)
		}
	}

	async getChartSettings(timeout: number = 5000): Promise<ChartSettings | null> {
		this.ensureConnected()
		try {
			// First check if chart settings are already available
			const existingSettings = this.broker.getChartSettings()
			if (existingSettings) {
				logger.debug('PocketOptionApi', 'Chart settings already available')
				return existingSettings
			}

			// Wait for chart settings with a timeout
			const chartSettings = await this.broker.waitForChartSettings(timeout)
			return chartSettings
		} catch (error) {
			logger.error('PocketOptionApi', 'Failed to get chart settings', error)
			throw new Error(`Failed to get chart settings: ${error instanceof Error ? error.message : 'Unknown error'}`)
		}
	}

	async selectAsset(assetSymbol: string, interval: number): Promise<void> {
		this.ensureConnected()
		try {
			await this.broker.selectAsset(assetSymbol, interval)
			logger.debug('PocketOptionApi', `Selected asset: ${assetSymbol} with interval ${interval}`)
		} catch (error) {
			logger.error('PocketOptionApi', 'Failed to select asset', error)
			throw new Error(`Failed to select asset: ${error instanceof Error ? error.message : 'Unknown error'}`)
		}
	}

	/**
	 * Enable or disable immediate trading mode
	 */
	setImmediateTradingMode(enabled: boolean): void {
		this.broker.setImmediateTradingMode(enabled)
	}

	/**
	 * Get immediate trading mode status
	 */
	getImmediateTradingMode(): boolean {
		return this.broker.getImmediateTradingMode()
	}

	async buy(payload: OrderPayload): Promise<void> {
		this.ensureConnected()
		try {
			await this.broker.placeOrder({
				...payload,
				action: Action.BUY
			})
			logger.debug('PocketOptionApi', `Placed buy order: ${JSON.stringify(payload)}`)
		} catch (error) {
			logger.error('PocketOptionApi', 'Failed to place buy order', error)
			throw new Error(`Failed to place buy order: ${error instanceof Error ? error.message : 'Unknown error'}`)
		}
	}

	/**
	 * Check if the API is connected to the broker
	 * @returns true if connected, false otherwise
	 */
	isApiConnected(): boolean {
		return this.isConnected
	}

	/**
	 * Get the underlying broker instance (use with caution)
	 * @returns The PocketOption broker instance
	 */
	getBroker(): PocketOption {
		return this.broker
	}

	/**
	 * Disconnect from the broker and cleanup resources
	 */
	async disconnect(): Promise<void> {
		try {
			// Note: PocketOption class doesn't have a disconnect method yet
			// This is a placeholder for future implementation
			this.isConnected = false
			logger.info('PocketOptionApi', 'Disconnected from broker')
		} catch (error) {
			logger.error('PocketOptionApi', 'Error during disconnect', error)
			throw error
		}
	}

	/**
	 * Ensure the API is connected before performing operations
	 * @throws Error if not connected
	 */
	private ensureConnected(): void {
		if (!this.isConnected) {
			throw new Error('API is not connected. Call connect() first.')
		}
	}
}
