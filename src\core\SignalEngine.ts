import { defaultStrategyConfig, strategyMap } from '../strategies'
import { logger } from '../utils/logger'

export class SignalEngine {
	private readonly candles: Candle[] = []
	private readonly strategyFn: (candles: Candle[], config: StrategyConfig, lastSignal: Signal) => SignalMeta
	private readonly config: StrategyConfig
	private readonly signalHistory: SignalMeta[] = []
	private lastSignal: Signal = undefined
	private lastMeta: SignalMeta = { signal: undefined, reason: 'Not evaluated yet', confidence: 'low' }

	constructor(strategyName: StrategyName, config?: Partial<StrategyConfig>) {
		const fn = strategyMap[strategyName]
		if (!fn) throw new Error(`Unknown strategy: ${strategyName}`)
		this.strategyFn = fn
		this.config = { ...defaultStrategyConfig[strategyName], ...config }
	}

	update(candle: Candle): SignalMeta {
		this.candles.push(candle)

		// Check if we have enough candles for meaningful signal generation
		const minCandles = this.getMinimumCandlesRequired()
		if (this.candles.length < minCandles) {
			const result: SignalMeta = {
				signal: undefined,
				reason: `Accumulating data: ${this.candles.length}/${minCandles} candles`,
				confidence: 'low'
			}
			this.lastMeta = result
			return result
		}

		const result = this.strategyFn(this.candles, this.config, this.lastSignal)

		// Only modify signal if it's a valid signal change
		if (result.signal !== undefined && result.signal !== this.lastSignal) {
			this.lastSignal = result.signal
		} else if (result.signal === this.lastSignal) {
			// Don't change to HOLD if it's the same valid signal
			result.reason += ' (confirmed)'
		}

		this.lastMeta = result
		this.signalHistory.push(result)
		return result
	}

	/**
	 * Calculate minimum candles required based on strategy configuration
	 */
	private getMinimumCandlesRequired(): number {
		// For SIMPLE_MOMENTUM strategy, only need 2 candles
		if ('minThreshold' in this.config) {
			return 1
		}

		// For SMA_CROSS strategy, we need at least slowPeriod + 1 candles
		if ('slowPeriod' in this.config) {
			return (this.config.slowPeriod as number) + 1
		}

		// For RSI_BB_SMA and other complex strategies, use the largest period + 1
		if ('rsiPeriod' in this.config || 'bbPeriod' in this.config || 'smaPeriod' in this.config) {
			const rsiPeriod = (this.config as any).rsiPeriod || 0
			const bbPeriod = (this.config as any).bbPeriod || 0
			const smaPeriod = (this.config as any).smaPeriod || 0
			return Math.max(rsiPeriod, bbPeriod, smaPeriod) + 1
		}

		// For other strategies, use a much lower default for immediate signals
		return 2 // Reduced from 21 to enable faster signal generation
	}

	getLastSignal(): Signal {
		return this.lastSignal
	}

	getLastMeta(): SignalMeta {
		return this.lastMeta
	}

	getSignalHistory(): SignalMeta[] {
		return this.signalHistory
	}

	reset(): void {
		this.candles.length = 0
		this.signalHistory.length = 0
		this.lastSignal = undefined
		this.lastMeta = { signal: undefined, reason: 'Reset', confidence: 'low' }
	}

	/**
	 * Preload historical candles without generating signals
	 * This is used to initialize the engine with historical data
	 */
	preloadCandles(candles: Candle[]): void {
		this.candles.length = 0 // Clear existing candles
		this.candles.push(...candles)
		logger.debug('SignalEngine', `Preloaded ${candles.length} historical candles`)
	}

	/**
	 * Get the number of candles currently stored
	 */
	getCandleCount(): number {
		return this.candles.length
	}

	/**
	 * Check if the engine has enough candles for reliable signal generation
	 */
	isReady(): boolean {
		return this.candles.length >= this.getMinimumCandlesRequired()
	}

	evaluatePerformance(correctSignals: Signal[], actualOutcomes: Signal[]): { accuracy: number } {
		let correct = 0
		const count = Math.min(correctSignals.length, actualOutcomes.length)
		for (let i = 0; i < count; i++) {
			if (correctSignals[i] === actualOutcomes[i]) correct++
		}
		return { accuracy: count > 0 ? correct / count : 0 }
	}

	static resolvePrioritySignal(signals: SignalMeta[], priorities: number[]): SignalMeta {
		let bestIndex = -1
		for (let i = 0; i < signals.length; i++) {
			const signal = signals[i]
			if (!signal || signal.signal === 'HOLD' || signal.signal === undefined) continue
			if (
				bestIndex === -1 ||
				(priorities[i] ?? 0) > (priorities[bestIndex] ?? 0) ||
				(priorities[i] ?? 0) === (priorities[bestIndex] ?? 0)
			) {
				bestIndex = i
			}
		}
		return bestIndex >= 0 ? signals[bestIndex]! : { signal: 'HOLD', reason: 'No dominant signal', confidence: 'low' }
	}
}
