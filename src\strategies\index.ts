import { evaluateBbStoch } from './bbStoch'
import { evaluateCciStoch } from './cciStoch'
import { evaluateKcCci } from './kcCci'
import { evaluateKcStoch } from './kcStoch'
import { evaluateMacdBbStoch } from './macdBbStoch'
import { evaluateMacdKc } from './macdKc'
import { evaluateMacdKcCci } from './macdKcCci'
import { evaluateMacdKcStoch } from './macdKcStoch'
import { evaluateMacdStoch } from './macdStoch'
import { evaluateRsiBbSma } from './rsiBbSma'
import { evaluateRsiMacdBbStoch } from './rsiMacdBbStoch'
import { evaluateSmaCross } from './smaCross'
import { evaluateStochCci } from './stochCci'
import { evaluateStochMacd } from './stochMacd'
import { evaluateSimpleMomentum } from './simpleMomentum'

export const strategyMap: Record<StrategyName, StrategyFn> = {
	SMA_CROSS: evaluateSmaCross,
	RSI_BB_SMA: evaluateRsiBbSma,
	RSI_MACD_BB_STOCH: evaluateRsiMacdBbStoch,
	MACD_KC_CCI: evaluateMacdKcCci,
	SIMPLE_MOMENTUM: evaluateSimpleMomentum,

	// Two-indicator strategies
	MACD_KC: evaluateMacdKc,
	MACD_STOCH: evaluateMacdStoch,
	BB_STOCH: evaluateBbStoch,
	KC_STOCH: evaluateKcStoch,
	KC_CCI: evaluateKcCci,
	STOCH_CCI: evaluateStochCci,
	CCI_STOCH: evaluateCciStoch,
	STOCH_MACD: evaluateStochMacd,

	// Three-indicator strategies
	MACD_BB_STOCH: evaluateMacdBbStoch,
	MACD_KC_STOCH: evaluateMacdKcStoch
}

// Default configurations for each strategy
export const defaultStrategyConfig: Record<StrategyName, StrategyConfig> = {
	SMA_CROSS: {
		fastPeriod: 2,
		slowPeriod: 3
	} as SmaCrossConfig,
	RSI_BB_SMA: {
		rsiPeriod: 3,
		bbPeriod: 3,
		smaPeriod: 3
	} as RsiBbSmaConfig,
	RSI_MACD_BB_STOCH: {
		rsiPeriod: 14,
		bbPeriod: 20,
		macdFast: 12,
		macdSlow: 26,
		macdSignal: 9,
		stochPeriod: 14,
		stochSignal: 3
	} as RsiMacdBbStochConfig,
	MACD_KC_CCI: {
		macdFast: 12,
		macdSlow: 26,
		macdSignal: 9,
		kcPeriod: 20,
		kcMultiplier: 2,
		cciPeriod: 20
	} as MacdKcCciConfig,
	// Two-indicator strategies
	MACD_KC: {
		macdFast: 12,
		macdSlow: 26,
		macdSignal: 9,
		kcPeriod: 20,
		kcMultiplier: 2
	} as MacdKcConfig,
	MACD_STOCH: {
		macdFast: 12,
		macdSlow: 26,
		macdSignal: 9,
		stochPeriod: 14,
		stochSignal: 3
	} as MacdStochConfig,
	BB_STOCH: {
		bbPeriod: 20,
		stochPeriod: 14,
		stochSignal: 3
	} as BbStochConfig,
	KC_STOCH: {
		kcPeriod: 20,
		kcMultiplier: 2,
		stochPeriod: 14,
		stochSignal: 3
	} as KcStochConfig,
	KC_CCI: {
		kcPeriod: 20,
		kcMultiplier: 2,
		cciPeriod: 20
	} as KcCciConfig,
	STOCH_CCI: {
		stochPeriod: 14,
		stochSignal: 3,
		cciPeriod: 20
	} as StochCciConfig,
	CCI_STOCH: {
		cciPeriod: 14,
		stochPeriod: 21,
		stochSignal: 5
	} as CciStochConfig,
	STOCH_MACD: {
		stochPeriod: 14,
		stochSignal: 3,
		macdFast: 12,
		macdSlow: 26,
		macdSignal: 9
	} as StochMacdConfig,
	// Three-indicator strategies
	MACD_BB_STOCH: {
		macdFast: 12,
		macdSlow: 26,
		macdSignal: 9,
		bbPeriod: 20,
		stochPeriod: 14,
		stochSignal: 3
	} as MacdBbStochConfig,
	MACD_KC_STOCH: {
		macdFast: 12,
		macdSlow: 26,
		macdSignal: 9,
		kcPeriod: 20,
		kcMultiplier: 2,
		stochPeriod: 14,
		stochSignal: 3
	} as MacdKcStochConfig,
	SIMPLE_MOMENTUM: {
		minThreshold: 0.01, // 0.01% minimum change to trigger signal
		mediumThreshold: 0.05, // 0.05% for medium confidence
		strongThreshold: 0.1 // 0.1% for high confidence
	} as SimpleMomentumConfig
}
